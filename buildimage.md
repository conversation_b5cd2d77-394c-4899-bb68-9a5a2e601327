rohan@DESKTOP-6PG5DVO MINGW64 /d/dev-dev/developer (main)
$ docker build -t prasha-chat-ai1 .
[+] Building 0.0s (0/0)                                                                                                         docker:desktop-linux
ERROR: failed to solve: Canceled: context canceled

rohan@DESKTOP-6PG5DVO MINGW64 /d/dev-dev/developer (main)
$ ^[[200~docker tag prasha-chat-ai1:latest 463470954735.dkr.ecr.us-east-1.amazonaws.com/prasha-chat-ai1:latest~^C

rohan@DESKTOP-6PG5DVO MINGW64 /d/dev-dev/developer (main)
$ ^[[200~aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin 463470954735.dkr.ecr.us-east-1.amazonaws.com~^C
rohan@DESKTOP-6PG5DVO MINGW64 /d/dev-dev/developer (main)
$ docker push 463470954735.dkr.ecr.us-east-1.amazonaws.com/prasha-chat-ai1:latest^C

rohan@DESKTOP-6PG5DVO MINGW64 /d/dev-dev/developer (main)
$ aws ecr describe-images --repository-name prasha-chat-ai1 --region us-east-1^C





docker build -t prasha-chat-ai1 .
docker tag prasha-chat-ai1:latest 183295433240.dkr.ecr.us-east-1.amazonaws.com/prasha-chat-final:latest
aws ecr describe-images --repository-name prasha-chat-final --region us-east-1   
docker push 183295433240.dkr.ecr.us-east-1.amazonaws.com/prasha-chat-final:latest